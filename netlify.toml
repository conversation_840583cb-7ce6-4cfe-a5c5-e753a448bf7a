# Global netlify settings for the whole repo

# Build settings
[build]
  # Dir where netlify will be looking for the project
  base = "."

[[headers]]
  for = "/*"
  [headers.values]
    X-Content-Type-Options = 'nosniff'
    X-frame-Options = 'SAMEORIGIN'

    Content-Security-Policy = """
    connect-src 'self' wss://staging-api.mytontine.com 
                      https://staging-api.mytontine.com 
                      https://ovh-test-api.mytontine.com
                      https://rumble-test-api.mytontine.com
                      wss://api.mytontine.com https://api.mytontine.com
                      https://o990791.ingest.sentry.io https://hl9czw39.api.sanity.io/ 
                      https://api.facetec.com
                      https://api.locize.app/
                      https://mixpanel.mytontine.com/
                      https://api-js.mixpanel.com
                      *.intercomassets.com
                      *.intercomcdn.com
                      *.intercom.io
                      wss://nexus-websocket-a.intercom.io;
    img-src 'self' data: blob: https://cdn.jsdelivr.net https://cdn.sanity.io https://purecatamphetamine.github.io/ *.intercomassets.com *.intercomcdn.com *.intercom.io; 
    media-src 'self' data: https://js.intercomcdn.com; 
    frame-ancestors 'self' https://tontine.com/ https://ira.tontine.com  https://tontineira.com https://staging-tontine-com.netlify.app/ http://localhost:3000 
                      https://app.netlify.com/ 
                      https://*.locize.app
                      sms:
                      https://incontext.locize.app
                      https://docuseal.eu;
    frame-src 'self'  https://api.facetec.com
                      https://app.netlify.com/
                      https://*.locize.app 
                      sms:
                      https://incontext.locize.app
                      https://tontine.com/
                      https://staging-tontine-com.netlify.app/
                      *.intercomassets.com
                      *.intercomcdn.com
                      *.intercom.io
                      https://docuseal.eu; 
    style-src-attr 'self' 'unsafe-inline'; 
    script-src 'self' 'unsafe-eval' https://api.facetec.com https://app.netlify.com/ 
                                    https://incontext.locize.app https://*.locize.app
                                    https://mixpanel.mytontine.com/
                                    *.intercomassets.com
                                    *.intercomcdn.com
                                    *.intercom.io;
    worker-src 'self' blob: https://api.facetec.com https://app.netlify.com/ https://incontext.locize.app;
    script-src-elem 'self' 'unsafe-inline' blob: *.intercomassets.com *.intercomcdn.com *.intercom.io;
    base-uri 'self';
    font-src 'self' *.intercomassets.com *.intercomcdn.com *.intercom.io https://fonts.gstatic.com;
    object-src 'none';
    """


# Runs on every open PR against `staging` branch
[context.deploy-preview]
  command = """
   npm ci && 
   npm run cypress:ct || 
   npm run cypress:ct &&
   npm run cypress:ut &&
   npm run build:app staging
   """
  publish = "/dist"


# Deploy settings and plugins for dev branch
[context.staging]
  command = """
   npm ci && 
   npm run cypress:ct || 
   npm run cypress:ct &&
   npm run cypress:ut &&
   npm run build:app staging
   """
  publish = "/dist"

[[context.staging.plugins]]
  package = "@sentry/netlify-build-plugin"
  [context.staging.plugins.inputs]
    sentryOrg = "tontinetrust"
    sentryProject = "my-tontine-web-application"
    deployPreviews = false

# Deploy settings and plugins for production branch
[context.prod-my-tontine]
  command = """
   npm ci && npm run build:app production
   """
  publish = "/dist"

[[context.prod-my-tontine.plugins]]
  package = "@sentry/netlify-build-plugin"
  [context.prod-my-tontine.plugins.inputs]
    sentryOrg = "tontinetrust"
    sentryProject = "my-tontine-web-application"
    deployPreviews = false

########### MyTontine Lite ###################
[context.staging-my-tontine-lite]
  command = """
   npm ci && 
   npm run build:app staging lite
   """
  publish = "/dist"

[context.prod-my-tontine-lite]
  command = """
   npm ci && npm run build:app production lite
   """
  publish = "/dist"

[[context.prod-my-tontine-lite.plugins]]
  package = "@sentry/netlify-build-plugin"
  [context.prod-my-tontine-lite.plugins.inputs]
    sentryOrg = "tontinetrust"
    sentryProject = "my-tontine-web-application"
    deployPreviews = false

