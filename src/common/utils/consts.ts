/**
 * Common constants used across the application
 */

const COMMON_CONSTANTS = {
  // Component behavior config
  DEBOUNCE_TIME: 1_000,
  SEARCH_DEBOUNCE_MS: 500,
  STEP_ONE: 1,
  STEP_TWO: 2,
  DISABLED: 'disabled',
  DEFAULT_STYLE: '',
  ENTER_KEY: 13,

  // Localization
  FALLBACK_LOCALE: 'en-US',
  FALLBACK_COUNTRY_CODE: 'USA',
  MAIN_APP_COUNTRY: 'USA',
  INVALID_DATE_ERROR_MSG: 'Invalid date',
  REQUIRED_FIELD: '*',
  NOT_APPLICABLE: 'N/A',
  READONLY_FIELD: 'read-only',

  // API operations
  CLOSE_MODAL_TIME: 5_000,

  // Formatter values
  CLOSURE_ACCOUNT_DATE_FORMAT: {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  },

  // Browser storage keys
  UNIQUE_ID_GENERATOR_RADIX: 36,
  DECIMAL_CUT_START: 2,
  DECIMAL_CUT_END: 15,
  FORM_AGREEMENTS_KEY: 'agreementsPage',
  VISITED_EXPLAINER_PAGE: 'visitedLegalPage',
  IP_COUNTRY: 'user_country',
  TONTINE_WEBSITE_ORIGIN: 'websiteOrigin',

  // Query params
  COUNTRY_Q: 'country',
} as const

const ENVIRONMENTS = {
  development: 'development',
  staging: 'staging',
  production: 'production',
} as const

const SUPPORTED_BROWSERS_LINKS = {
  GOOGLE_CHROME: 'https://www.google.com/chrome/',
  MOZILLA_FIREFOX: 'https://www.mozilla.org/en-US/firefox/new/',
  OPERA: 'https://www.opera.com/',
  SAFARI: 'https://www.apple.com/safari/',
} as const

const MAGIC_LOGIN_PARAM = 'magic_login' as const

// Intercom
const INTERCOM_APP_ID = import.meta.env?.VITE_INTERCOM_APP_ID ?? ''

// Date constants
const START_DAY = 1
const JANUARY = 1
const FEBRUARY = 2
const DECEMBER = 12
const DEFAULT_YEAR_MIN = 1900
const DEFAULT_YEAR_MAX = 2100

// Filter types
const filterRangeTypes = [
  {
    label: 'FILTER_DATETYPE_TODAY',
    type: 'today',
  },
  {
    label: 'FILTER_DATETYPE_YESTERDAY',
    type: 'yesterday',
  },
  {
    label: 'FILTER_DATETYPE_LAST_7_DAYS',
    type: 'last7Days',
  },
  {
    label: 'FILTER_DATETYPE_LAST_30_DAYS',
    type: 'last30Days',
  },
  {
    label: 'FILTER_DATETYPE_CUSTOM_RANGE',
    type: 'customRange',
  },
] as const

const FilterTypeToNumber = {
  GREATER_THAN: 1,
  LESS_THAN: 2,
  EQUAL: 3,
  BETWEEN: 4,
} as const

const DateRangeToNumber = {
  today: 0,
  last7Days: 7,
  last30Days: 30,
} as const

const APP_LINKS = {
  // Add proper link when available on the app store
  ios: 'https://apps.apple.com/app-id',
  android:
    'https://play.google.com/store/apps/details?id=com.tontinetrust.mytontine',
} as const

export {
  APP_LINKS,
  COMMON_CONSTANTS,
  DateRangeToNumber,
  DECEMBER,
  DEFAULT_YEAR_MAX,
  DEFAULT_YEAR_MIN,
  ENVIRONMENTS,
  FEBRUARY,
  filterRangeTypes,
  FilterTypeToNumber,
  INTERCOM_APP_ID,
  JANUARY,
  MAGIC_LOGIN_PARAM,
  START_DAY,
  SUPPORTED_BROWSERS_LINKS,
}
