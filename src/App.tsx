import { StrictMode, Suspense } from 'react'
import 'react-toastify/dist/ReactToastify.css'
import './common/style/main.scss'
// Localization
import './config/i18n'

import { BrowserRouter } from 'react-router'
import { ToastContainer } from 'react-toastify'
import InitializeUI from './common/components/InitializeUI'
import NavBar from './common/components/NavBar'
import OpenInApp from './common/components/OpenInApp'
import SuspenseLoader from './common/components/SuspenseLoader'
import { useDeviceScreen } from './common/hooks/useDeviceScreen'
// Import useTranslate hook
import { useTranslate } from './common/hooks/useTranslate'
import { isLite } from './config/lite'
import AccessibilityProvider from './features/accessibility/AccessibilityProvider'
import AccessibilityControls from './features/accessibility/components/AccessibilityControls'
import { LegalServiceProvider } from './features/agreements/LegalServiceProvider'
import { AccountServiceProvider } from './features/authentication/AccountServiceProvider'
import SessionExtensionModal from './features/authentication/components/SessionExtensionModal'
import LiteAuthProvider from './features/authentication/LiteAuthProvider'
import MagicLogin from './features/authentication/pages/MagicLogin'
import { BankingServiceProvider } from './features/banking/BankingServiceProvider'
import FeedbackCard from './features/feedback/components/FeedbackCard'
import PageRoutes from './routes/PageRoutes'

/**
 * MyTontine app
 */
export default function App() {
  const { isMobileOrTablet } = useDeviceScreen()
  const t = useTranslate()

  // Example translation keys
  const metaTitle = t('SEO.META_TITLE')
  const metaDescription = t('SEO.META_DESCRIPTION')

  return (
    <StrictMode>
      {/* Dynamic meta tags */}
      <title>{metaTitle}</title>
      <meta name="title" content={metaTitle} />
      <meta name="description" content={metaDescription} />

      <meta property="og:type" content="website" />
      <meta property="og:url" content="https://app.tontineira.com/" />
      <meta property="og:title" content={metaTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta
        property="og:image"
        content="https://cdn.sanity.io/images/hl9czw39/production/2f49578649383c50fd9114b1f284aa56b51a302f-1014x630.png"
      />

      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content="https://app.tontineira.com/" />
      <meta property="twitter:title" content={metaTitle} />
      <meta property="twitter:description" content={metaDescription} />
      <meta
        property="twitter:image"
        content="https://cdn.sanity.io/images/hl9czw39/production/2f49578649383c50fd9114b1f284aa56b51a302f-1014x630.png"
      />
      <BrowserRouter>
        <AccessibilityProvider>
          <AccountServiceProvider>
            <BankingServiceProvider>
              <LegalServiceProvider>
                <LiteAuthProvider>
                  <Suspense fallback={<SuspenseLoader />}>
                    {!isLite && <MagicLogin />}
                    <InitializeUI>
                      {!isLite && <OpenInApp />}
                      {!isLite && <NavBar />}
                      {!isLite && <SessionExtensionModal />}

                      <PageRoutes />
                      <ToastContainer
                        position={isMobileOrTablet ? 'top-center' : 'top-right'}
                        autoClose={5_000}
                        theme="colored"
                      />
                      {!isLite && <FeedbackCard />}
                      {isLite && (
                        <AccessibilityControls
                          variant="gray-dark"
                          position="bottom-left"
                        />
                      )}
                    </InitializeUI>
                  </Suspense>
                </LiteAuthProvider>
              </LegalServiceProvider>
            </BankingServiceProvider>
          </AccountServiceProvider>
        </AccessibilityProvider>
      </BrowserRouter>
    </StrictMode>
  )
}
