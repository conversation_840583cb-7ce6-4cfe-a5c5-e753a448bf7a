import axios from 'axios'
import { API } from '../../common/api/API'
import { axiosConfig } from '../../common/api/RequestConfig'
import { API_STATUS } from '../../common/constants/ApiErrors'
import { writeToConsoleAndIssueAlert } from '../../common/utils/UtilFunctions'
import { isLite } from '../../config/lite'
import { getAuthToken } from '../authentication/utils/AuthFunctions'
import { parsePayloadToParams, processAndFlatAgreement } from './LegalUtils'
import {
  Agreement,
  AgreementContents,
  LegalMachineContext,
  LegalMachineEvent,
  SubmissionResponse,
} from './types/LegalMachineTypes.types'

const withCredentials = false

/**
 * Fetches an agreement containing legal text
 */
const fetchAgreement = async (
  _: LegalMachineContext,
  event: LegalMachineEvent
) => {
  try {
    const { agreementTypes } = event?.payload as {
      agreementTypes: Array<Agreement>
    }

    if (agreementTypes?.length <= 0) {
      throw new TypeError(
        `At least 1 agreement type is needed in order to get an agreement`
      )
    }

    const response = await axios.get(
      `${API.getAgreement}/${parsePayloadToParams(agreementTypes)}/latest`,
      axiosConfig({
        signal: event?.payload?.abortController?.signal,
        withCredentials,
        authToken: isLite ? undefined : getAuthToken(),
      })
    )

    const { status, data } = response as {
      status: number
      data: Array<[Agreement, AgreementContents]>
    }

    const processedResponse = processAndFlatAgreement(data)

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(processedResponse)

      return processedResponse
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.abortController?.abort()
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

/**
 * Signs an agreement using an unique agreement type
 */
const signAgreement = async (
  _: LegalMachineContext,
  event: LegalMachineEvent
) => {
  try {
    if (
      !event?.payload ||
      !event?.payload?.signedAgreementData ||
      !event?.payload?.agreementType
    ) {
      throw new TypeError(
        `No agreementData or type found, check payload got >> ${JSON.stringify(event?.payload)} << `
      )
    }

    const { signedAgreementData, agreementType } = event.payload

    const version =
      signedAgreementData?.signedAgreementContents?.[agreementType]?.version

    const response = await axios.put(
      `${API.signAgreement}/${agreementType}/${version}`,
      null,
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )

    const { status } = response

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(event?.payload)

      return event?.payload
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

const fetchDocumentEmbedUrl = async (
  _: LegalMachineContext,
  event: LegalMachineEvent
) => {
  try {
    if (!event?.payload?.submissionData) {
      throw new TypeError(
        `No submission data found, check payload got >> ${JSON.stringify(event?.payload)} << `
      )
    }

    const { submissionData } = event.payload

    const response = await axios.post(
      API.getDocumentEmbedUrl,
      submissionData,
      axiosConfig({ withCredentials, authToken: getAuthToken() })
    )

    const { status, data } = response as {
      status: number
      data: SubmissionResponse
    }

    if (status === API_STATUS.OK) {
      event?.payload?.successCallback?.(data)
      return data
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      failureCallback: event?.payload?.failureCallback,
    })
  } finally {
    event?.payload?.finallyCallback?.()
  }

  return undefined
}

export { fetchAgreement, fetchDocumentEmbedUrl, signAgreement }
