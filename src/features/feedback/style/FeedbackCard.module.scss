@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';


/** @define feedbackCard */
.feedbackCard {
    writing-mode: vertical-lr;
    text-orientation: mixed;
    min-width: unset;
    width: fit-content;
    height: fit-content;
    position: fixed;
    left: 0;
    right: 0;
    margin-left: auto;
    top: 30%;
    @include mixins.flex-layout(row-reverse, space-around, $gap: 1.25rem);
    @include mixins.card-interact(pointer, colors.$green, colors.$white);

    //Mobile devices scss starts from here
    @media only screen and (max-width: variables.$mobile-devices) {
        display: none;
    }


    &:hover,
    &:active {
        opacity: 0.7;
        @include mixins.card-interact(pointer, colors.$green, colors.$white);
    }

    &:active {
        scale: 1.05;
    }

    &__content {
        transform: rotate(180deg) !important;
    }

    &__info {
        transform: rotate(-180deg);
    }
}