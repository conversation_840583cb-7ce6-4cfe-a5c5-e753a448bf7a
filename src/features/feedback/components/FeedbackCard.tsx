import { useState } from 'react'

import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardInfo,
} from '../../../common/components/card/Card'
import TontineModal from '../../../common/components/Modal'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import style from '../style/FeedbackCard.module.scss'
import UserFeedback from './UserFeedback'

/** Feedback card only rendered when the user is in desktop mode and
 * authenticated. When clicked opens a modal used to provide feedback
 * about the user experience with the app
 */
const FeedbackCard = () => {
  const t = useTranslate()
  const { isAuthenticated } = useAccountService()
  const [openModal, setOpenModal] = useState(false)

  if (isAuthenticated) {
    return (
      <>
        <Card
          onClick={() => setOpenModal(true)}
          variant="gray-dirty"
          className={style[`feedbackCard`]}
        >
          <CardHeader
            icon={ASSET.iconmileureymodal}
            className={style[`feedbackCard__header`]}
          />

          <CardContent
            className={style[`feedbackCard__content`]}
            variant="gray-dirty"
            title={t('FEEDBACK_CARD.TITLE')}
          />

          <CardFooter className={style[`feedbackCard__footer`]}>
            <CardInfo showArrow className={style[`feedbackCard__info`]} />
          </CardFooter>
        </Card>

        {openModal && (
          <TontineModal
            // Renders the `<UserFeedback />` component as a modal
            isOpen={openModal}
            backdrop
            hasOnlyContent
            onOutsideModalContentClick={() => setOpenModal(false)}
          >
            <UserFeedback
              ratingOpenDefault
              closeModal={() => setOpenModal(false)}
              onSuccessfulSubmit={() => setOpenModal(false)}
            />
          </TontineModal>
        )}
      </>
    )
  }

  return null
}

export default FeedbackCard
