import { useState } from 'react'
import { useSupportedCountries } from '../../../common/hooks/useSupportedCountries'
import { postMessageToParent } from '../../../common/hooks/useWindowPostMessage'
import {
  IncomeForecastParams,
  InvestmentStrategyId,
} from '../../../common/types/CommonTypes.types'
import { InvestmentDetails } from '../../authentication/types/AuthMachineTypes.type'
import { useBankingService } from '../../banking/hooks/useBankingService'
import LiteReferralLayout from '../../referral/components/LiteReferralLayout'
import MtlCompareBtns from '../components/MtlCompareBtns'
import MtlHomeButtons from '../components/MtlHomeButtons'
import ParamModes from '../components/ParamModes'
import PensionPlanDashboard from '../components/PensionPlanDashboard'
import TontinatorDashboard from '../components/TontinatorDashboard'
import {
  chooseDefaultParams,
  modifyParamsForComparison,
} from '../hooks/usePreRegisterForecast'
import bottomStyle from '../style/BottomCtaLiteLayout.module.scss'
import style from '../style/PublicTontinatorPage.module.scss'
import { PublicTontinatorProps } from '../types/PublicTontinator.types'
import PublicTontinatorInputLayout from './PublicTontinatorInputLayout'

const idToProd = {
  TTF: 'TontineTrustFund',
  IRA: 'TontineIRA',
}

/**
 * Tontinator page for public users
 */
const PublicTontinator = ({
  incomeForecastParams,
  setIncomeForecastParams,
  setRegisterForm,
  blueForecastParams,
  setBlueForecastParams,
  yellowForecastParams,
  setYellowForecastParams,
}: PublicTontinatorProps) => {
  const { sendBankEvent } = useBankingService()
  const [isCompareOpen, setIsCompareOpen] = useState(false)
  const [openSliderPage, setOpenSliderPage] = useState(false)
  const { supportedCountry } = useSupportedCountries({
    alpha3CountryCode: incomeForecastParams?.countryOfResidence,
  })

  return (
    <main className={style['public-tontinator-page']}>
      <section
        className={style['public-tontinator-page__tontinator-container']}
      >
        <section className={style['public-tontinator-page__chart-layout']}>
          <ParamModes
            onChange={(value) => {
              sendBankEvent({
                type: 'UPDATE_PRODUCT',
                payload: {
                  product: idToProd[value],
                },
              })
              const defaultParams = chooseDefaultParams({
                tontinatorParams: supportedCountry.tontinatorParams,
                supportedCountry,
              })

              setIncomeForecastParams({
                // reset to default params when the user changes the product
                // also known as params mode
                ...defaultParams,
                paramsMode: value,
              } as IncomeForecastParams)

              setBlueForecastParams({
                ...defaultParams,
                paramsMode: value,
              } as IncomeForecastParams)

              setYellowForecastParams({
                ...modifyParamsForComparison({
                  retirementAge: defaultParams?.retirementAge,
                  targetValue:
                    supportedCountry?.tontinatorParams?.maxRetirementAge?.age ??
                    0,
                  targetIncrement: 5,
                  strategies:
                    supportedCountry?.supportedInvestments as InvestmentDetails,
                  strategy: defaultParams?.strategy as InvestmentStrategyId,
                }),
                paramsMode: value,
              } as IncomeForecastParams)
            }}
            activeMode={incomeForecastParams.paramsMode ?? 'TTF'}
          />
          {isCompareOpen ? (
            <PensionPlanDashboard
              dataToDraw={[blueForecastParams, yellowForecastParams]}
            />
          ) : (
            <TontinatorDashboard incomeForecastParams={incomeForecastParams} />
          )}
        </section>

        <PublicTontinatorInputLayout
          isSliderPageOpen={openSliderPage}
          setOpenSliderPage={setOpenSliderPage}
          setRegisterForm={setRegisterForm}
          incomeForecastParams={incomeForecastParams}
          setIncomeForecastParams={setIncomeForecastParams}
          comparison={isCompareOpen}
          blueForecastParams={blueForecastParams}
          setBlueForecastParams={setBlueForecastParams}
          yellowForecastParams={yellowForecastParams}
          setYellowForecastParams={setYellowForecastParams}
        />
      </section>

      <section
        className={style['public-tontinator-page__bottom-cta-container']}
      >
        <section className={bottomStyle[`bottom-cta-lite-layout`]}>
          {!isCompareOpen && (
            <MtlHomeButtons
              incomeForecastParams={incomeForecastParams}
              onSeeOtherScenarios={() => {
                setOpenSliderPage(true)
                postMessageToParent({
                  eventId: 'SCROLL_TO_TOP_TON',
                })
              }}
              onCompareChoices={() => {
                setIsCompareOpen(true)
                postMessageToParent({
                  eventId: 'SCROLL_TO_TOP_TON',
                })
              }}
              onClickSignUpButton={() => {
                // Modern approach using window.open with security features
                const windowFeatures = 'noopener,noreferrer'
                const handle = window.open(
                  'https://app.mytontine.com/signup',
                  '_blank',
                  windowFeatures
                )

                // Additional security: reset opener property
                if (handle) {
                  handle.opener = null
                }
              }}
              setIsOpenSignInModal={() => {
                // Modern approach using window.open with security features
                const windowFeatures = 'noopener,noreferrer'
                const handle = window.open(
                  'https://app.mytontine.com/signin',
                  '_blank',
                  windowFeatures
                )

                // Additional security: reset opener property
                if (handle) {
                  handle.opener = null
                }
              }}
            />
          )}

          {isCompareOpen && (
            <MtlCompareBtns
              onClickPlan2={() => {
                setIsCompareOpen(false)
                setIncomeForecastParams(yellowForecastParams)
                postMessageToParent({
                  eventId: 'SCROLL_TO_TOP_TON',
                })
              }}
              onSeeOtherScenarios={() => {
                setOpenSliderPage(true)
                postMessageToParent({
                  eventId: 'SCROLL_TO_TOP_TON',
                })
              }}
              onClickBack={() => {
                setIsCompareOpen(false)
                postMessageToParent({
                  eventId: 'SCROLL_TO_TOP_TON',
                })
              }}
              blueForecastParams={blueForecastParams}
              yellowForecastParams={yellowForecastParams}
              onClickPlan1={() => {
                setIsCompareOpen(false)
                setIncomeForecastParams(blueForecastParams)
                postMessageToParent({
                  eventId: 'SCROLL_TO_TOP_TON',
                })
              }}
            />
          )}
        </section>

        {!isCompareOpen && <LiteReferralLayout />}
      </section>
    </main>
  )
}

export default PublicTontinator
